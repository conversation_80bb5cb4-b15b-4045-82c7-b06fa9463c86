# Gunicorn configuration file for ForgeX Django application

# Server socket
bind = "127.0.0.1:8000"
backlog = 2048

# Worker processes
workers = 3
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "/mnt/d/DEV-College/ForgeX/ForgeX/logs/gunicorn_access.log"
errorlog = "/mnt/d/DEV-College/ForgeX/ForgeX/logs/gunicorn_error.log"
loglevel = "info"

# Process naming
proc_name = "forgex_gunicorn"

# Server mechanics
daemon = False
pidfile = "/mnt/d/DEV-College/ForgeX/ForgeX/gunicorn.pid"
user = "soul"
group = "soul"
tmp_upload_dir = None

# SSL (if needed later)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
