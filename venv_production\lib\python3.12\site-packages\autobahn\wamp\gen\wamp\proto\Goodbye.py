# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Goodbye(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Goodbye()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsGoodbye(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Goodbye
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Goodbye
    def Session(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Goodbye
    def Reason(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Goodbye
    def Message(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Goodbye
    def Resumable(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

def GoodbyeStart(builder): builder.StartObject(4)
def Start(builder):
    return GoodbyeStart(builder)
def GoodbyeAddSession(builder, session): builder.PrependUint64Slot(0, session, 0)
def AddSession(builder, session):
    return GoodbyeAddSession(builder, session)
def GoodbyeAddReason(builder, reason): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(reason), 0)
def AddReason(builder, reason):
    return GoodbyeAddReason(builder, reason)
def GoodbyeAddMessage(builder, message): builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(message), 0)
def AddMessage(builder, message):
    return GoodbyeAddMessage(builder, message)
def GoodbyeAddResumable(builder, resumable): builder.PrependBoolSlot(3, resumable, 0)
def AddResumable(builder, resumable):
    return GoodbyeAddResumable(builder, resumable)
def GoodbyeEnd(builder): return builder.EndObject()
def End(builder):
    return GoodbyeEnd(builder)