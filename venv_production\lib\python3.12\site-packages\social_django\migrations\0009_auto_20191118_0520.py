# Generated by Django 2.2.7 on 2019-11-18 05:20

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("social_django", "0008_partial_timestamp"),
    ]

    operations = [
        migrations.AddField(
            model_name="usersocialauth",
            name="created",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="usersocialauth",
            name="modified",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
