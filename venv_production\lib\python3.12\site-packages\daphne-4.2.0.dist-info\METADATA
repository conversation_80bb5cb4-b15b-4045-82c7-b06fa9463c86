Metadata-Version: 2.4
Name: daphne
Version: 4.2.0
Summary: Django ASGI (HTTP/WebSocket) server
Author-email: Django Software Foundation <<EMAIL>>
License: BSD
Project-URL: homepage, https://github.com/django/daphne
Project-URL: documentation, https://channels.readthedocs.io
Project-URL: repository, https://github.com/django/daphne.git
Project-URL: changelog, https://github.com/django/daphne/blob/main/CHANGELOG.txt
Project-URL: issues, https://github.com/django/daphne/issues
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.9
License-File: LICENSE
Requires-Dist: asgiref<4,>=3.5.2
Requires-Dist: autobahn>=22.4.2
Requires-Dist: twisted[tls]>=22.4
Provides-Extra: tests
Requires-Dist: django; extra == "tests"
Requires-Dist: hypothesis; extra == "tests"
Requires-Dist: pytest; extra == "tests"
Requires-Dist: pytest-asyncio; extra == "tests"
Requires-Dist: pytest-cov; extra == "tests"
Requires-Dist: black; extra == "tests"
Requires-Dist: tox; extra == "tests"
Requires-Dist: flake8; extra == "tests"
Requires-Dist: flake8-bugbear; extra == "tests"
Requires-Dist: mypy; extra == "tests"
Dynamic: license-file
