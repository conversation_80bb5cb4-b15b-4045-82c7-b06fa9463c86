# Generated by Django 3.2 on 2021-09-23 12:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("social_django", "0010_uid_db_index"),
    ]

    operations = [
        migrations.AlterField(
            model_name="association",
            name="id",
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID"),
        ),
        migrations.AlterField(
            model_name="code",
            name="id",
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID"),
        ),
        migrations.AlterField(
            model_name="nonce",
            name="id",
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID"),
        ),
        migrations.AlterField(
            model_name="partial",
            name="id",
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID"),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name="usersocialauth",
            name="id",
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID"),
        ),
    ]
