Metadata-Version: 2.2
Name: social-auth-app-django
Version: 5.4.3
Summary: Python Social Authentication, Django integration.
Author-email: <PERSON><PERSON> <<EMAIL>>
License: BSD
Project-URL: Homepage, https://github.com/python-social-auth/social-app-django
Keywords: django,openid,oauth,saml,social auth
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Internet
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: Django>=3.2
Requires-Dist: social-auth-core~=4.4
Provides-Extra: dev
Requires-Dist: coverage>=3.6; extra == "dev"

# Python Social Auth - Django

Python Social Auth is an easy to setup social authentication/registration
mechanism with support for several frameworks and auth providers.

## Description

This is the [Django](https://www.djangoproject.com/) component of the
[python-social-auth ecosystem](https://github.com/python-social-auth/social-core),
it implements the needed functionality to integrate
[social-auth-core](https://github.com/python-social-auth/social-core)
in a Django based project.

## Django version

This project will focus on the currently supported Django releases as
stated on the [Django Project Supported Versions table](https://www.djangoproject.com/download/#supported-versions).

Backward compatibility with unsupported versions won't be enforced.

## Documentation

Project documentation is available at https://python-social-auth.readthedocs.io/.

## Setup

```shell
$ pip install social-auth-app-django
```

## Contributing

Contributions are welcome!

Only the core and Django modules are currently in development. All others are in maintenance only mode, and maintainers are especially welcome there.

See the [https://github.com/python-social-auth/.github/blob/main/CONTRIBUTING.md](CONTRIBUTING.md) document for details.

## Versioning

This project follows [Semantic Versioning 2.0.0](https://semver.org/spec/v2.0.0.html).

## License

This project follows the BSD license. See the [LICENSE](LICENSE) for details.

## Donations

This project welcomes donations to make the development sustainable, you can fund Python Social Auth on following platforms:

- [GitHub Sponsors](https://github.com/sponsors/python-social-auth/)
- [Open Collective](https://opencollective.com/python-social-auth)
